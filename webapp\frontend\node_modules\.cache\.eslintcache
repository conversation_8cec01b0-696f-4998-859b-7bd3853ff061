[{"C:\\CMS\\webapp\\frontend\\src\\index.js": "1", "C:\\CMS\\webapp\\frontend\\src\\App.js": "2", "C:\\CMS\\webapp\\frontend\\src\\context\\GlobalContext.js": "3", "C:\\CMS\\webapp\\frontend\\src\\context\\AuthContext.js": "4", "C:\\CMS\\webapp\\frontend\\src\\pages\\LoginPageNew.js": "5", "C:\\CMS\\webapp\\frontend\\src\\pages\\Dashboard.js": "6", "C:\\CMS\\webapp\\frontend\\src\\components\\ProtectedRoute.js": "7", "C:\\CMS\\webapp\\frontend\\src\\services\\authService.js": "8", "C:\\CMS\\webapp\\frontend\\src\\pages\\AdminPage.js": "9", "C:\\CMS\\webapp\\frontend\\src\\components\\TopNavbar.js": "10", "C:\\CMS\\webapp\\frontend\\src\\pages\\CaviPage.js": "11", "C:\\CMS\\webapp\\frontend\\src\\pages\\HomePage.js": "12", "C:\\CMS\\webapp\\frontend\\src\\pages\\TestBobinePage.js": "13", "C:\\CMS\\webapp\\frontend\\src\\pages\\UserPage.js": "14", "C:\\CMS\\webapp\\frontend\\src\\pages\\cavi\\ParcoCaviPage.js": "15", "C:\\CMS\\webapp\\frontend\\src\\pages\\cavi\\CertificazioneCaviPage.js": "16", "C:\\CMS\\webapp\\frontend\\src\\components\\admin\\UserExpirationChecker.js": "17", "C:\\CMS\\webapp\\frontend\\src\\pages\\cavi\\GestioneComandeePage.js": "18", "C:\\CMS\\webapp\\frontend\\src\\pages\\cavi\\VisualizzaCaviPage.js": "19", "C:\\CMS\\webapp\\frontend\\src\\config.js": "20", "C:\\CMS\\webapp\\frontend\\src\\pages\\cavi\\TestCaviPage.js": "21", "C:\\CMS\\webapp\\frontend\\src\\services\\axiosConfig.js": "22", "C:\\CMS\\webapp\\frontend\\src\\pages\\CertificazioniPageDebug.jsx": "23", "C:\\CMS\\webapp\\frontend\\src\\pages\\cavi\\ReportCaviPageNew.js": "24", "C:\\CMS\\webapp\\frontend\\src\\pages\\cavi\\parco\\EliminaBobinaPage.js": "25", "C:\\CMS\\webapp\\frontend\\src\\pages\\cavi\\parco\\VisualizzaBobinePage.js": "26", "C:\\CMS\\webapp\\frontend\\src\\pages\\cavi\\parco\\CreaBobinaPage.js": "27", "C:\\CMS\\webapp\\frontend\\src\\pages\\cavi\\parco\\StoricoUtilizzoPage.js": "28", "C:\\CMS\\webapp\\frontend\\src\\pages\\cavi\\parco\\ModificaBobinaPage.js": "29", "C:\\CMS\\webapp\\frontend\\src\\pages\\cavi\\posa\\MetriPosatiSemplificatoPage.js": "30", "C:\\CMS\\webapp\\frontend\\src\\pages\\cavi\\posa\\ModificaBobinaPage.js": "31", "C:\\CMS\\webapp\\frontend\\src\\pages\\cavi\\posa\\ModificaCavoPage.js": "32", "C:\\CMS\\webapp\\frontend\\src\\pages\\cavi\\posa\\InserisciMetriPage.js": "33", "C:\\CMS\\webapp\\frontend\\src\\pages\\cavi\\posa\\CollegamentiPage.js": "34", "C:\\CMS\\webapp\\frontend\\src\\pages\\cantieri\\CantierePage.js": "35", "C:\\CMS\\webapp\\frontend\\src\\services\\excelService.js": "36", "C:\\CMS\\webapp\\frontend\\src\\services\\cantieriService.js": "37", "C:\\CMS\\webapp\\frontend\\src\\components\\admin\\ImpersonateUser.js": "38", "C:\\CMS\\webapp\\frontend\\src\\components\\admin\\UsersList.js": "39", "C:\\CMS\\webapp\\frontend\\src\\components\\admin\\UserForm.js": "40", "C:\\CMS\\webapp\\frontend\\src\\components\\admin\\DatabaseView.js": "41", "C:\\CMS\\webapp\\frontend\\src\\components\\admin\\ResetDatabase.js": "42", "C:\\CMS\\webapp\\frontend\\src\\components\\common\\SelectedCantiereDisplay.js": "43", "C:\\CMS\\webapp\\frontend\\src\\components\\cavi\\ExcelPopup.js": "44", "C:\\CMS\\webapp\\frontend\\src\\services\\userService.js": "45", "C:\\CMS\\webapp\\frontend\\src\\components\\common\\AdminHomeButton.js": "46", "C:\\CMS\\webapp\\frontend\\src\\components\\cavi\\TestBobineComponent.js": "47", "C:\\CMS\\webapp\\frontend\\src\\components\\cavi\\CertificazioneCavi.js": "48", "C:\\CMS\\webapp\\frontend\\src\\components\\cavi\\GestioneComande.js": "49", "C:\\CMS\\webapp\\frontend\\src\\components\\cavi\\ParcoCavi.js": "50", "C:\\CMS\\webapp\\frontend\\src\\components\\cavi\\PosaCaviCollegamenti.js": "51", "C:\\CMS\\webapp\\frontend\\src\\services\\apiService.js": "52", "C:\\CMS\\webapp\\frontend\\src\\services\\caviService.js": "53", "C:\\CMS\\webapp\\frontend\\src\\components\\cavi\\CavoForm.js": "54", "C:\\CMS\\webapp\\frontend\\src\\components\\cavi\\CaviFilterableTable.js": "55", "C:\\CMS\\webapp\\frontend\\src\\components\\common\\FilterableTable.js": "56", "C:\\CMS\\webapp\\frontend\\src\\services\\reportService.js": "57", "C:\\CMS\\webapp\\frontend\\src\\components\\charts\\ProgressChart.js": "58", "C:\\CMS\\webapp\\frontend\\src\\utils\\validationUtils.js": "59", "C:\\CMS\\webapp\\frontend\\src\\components\\charts\\TimelineChart.js": "60", "C:\\CMS\\webapp\\frontend\\src\\components\\charts\\BoqChart.js": "61", "C:\\CMS\\webapp\\frontend\\src\\components\\charts\\BobineChart.js": "62", "C:\\CMS\\webapp\\frontend\\src\\components\\charts\\CaviStatoChart.js": "63", "C:\\CMS\\webapp\\frontend\\src\\components\\certificazioni\\StrumentiList.jsx": "64", "C:\\CMS\\webapp\\frontend\\src\\components\\certificazioni\\StrumentoForm.jsx": "65", "C:\\CMS\\webapp\\frontend\\src\\components\\certificazioni\\CertificazioneForm.jsx": "66", "C:\\CMS\\webapp\\frontend\\src\\components\\certificazioni\\CertificazioniList.jsx": "67", "C:\\CMS\\webapp\\frontend\\src\\components\\cavi\\MetriPosatiSemplificatoForm.js": "68", "C:\\CMS\\webapp\\frontend\\src\\components\\cavi\\SelezionaCavoForm.js": "69", "C:\\CMS\\webapp\\frontend\\src\\components\\cavi\\CollegamentiCavo.js": "70", "C:\\CMS\\webapp\\frontend\\src\\components\\cavi\\ModificaBobinaForm.js": "71", "C:\\CMS\\webapp\\frontend\\src\\components\\cavi\\InserisciMetriForm.js": "72", "C:\\CMS\\webapp\\frontend\\src\\services\\adminService.js": "73", "C:\\CMS\\webapp\\frontend\\src\\services\\certificazioneService.js": "74", "C:\\CMS\\webapp\\frontend\\src\\services\\comandeService.js": "75", "C:\\CMS\\webapp\\frontend\\src\\utils\\navigationUtils.js": "76", "C:\\CMS\\webapp\\frontend\\src\\services\\parcoCaviService.js": "77", "C:\\CMS\\webapp\\frontend\\src\\utils\\bobinaValidationUtils.js": "78", "C:\\CMS\\webapp\\frontend\\src\\utils\\dateUtils.js": "79", "C:\\CMS\\webapp\\frontend\\src\\components\\cavi\\ConfigurazioneDialog.js": "80", "C:\\CMS\\webapp\\frontend\\src\\components\\cavi\\BobineFilterableTable.js": "81", "C:\\CMS\\webapp\\frontend\\src\\components\\cavi\\QuickAddCablesDialog.js": "82", "C:\\CMS\\webapp\\frontend\\src\\components\\common\\FilterableTableHeader.js": "83", "C:\\CMS\\webapp\\frontend\\src\\utils\\stateUtils.js": "84", "C:\\CMS\\webapp\\frontend\\src\\components\\cavi\\IncompatibleReelDialog.js": "85", "C:\\CMS\\webapp\\frontend\\src\\components\\cavi\\CavoDetailsView.js": "86", "C:\\CMS\\webapp\\frontend\\src\\components\\common\\ExcelLikeFilter.js": "87", "C:\\CMS\\webapp\\frontend\\src\\components\\cantieri\\CantieriFilterableTable.js": "88", "C:\\CMS\\webapp\\frontend\\src\\components\\cantieri\\EditCantiereDialog.js": "89", "C:\\CMS\\webapp\\frontend\\src\\components\\cantieri\\PasswordManagementDialog.js": "90", "C:\\CMS\\webapp\\frontend\\src\\components\\cantieri\\HoldToViewButton.js": "91", "C:\\CMS\\webapp\\frontend\\src\\components\\common\\MetricCard.js": "92", "C:\\CMS\\webapp\\frontend\\src\\components\\common\\EmptyState.js": "93", "C:\\CMS\\webapp\\frontend\\src\\components\\common\\ReportSection.js": "94"}, {"size": 557, "mtime": 1746952718482, "results": "95", "hashOfConfig": "96"}, {"size": 2728, "mtime": 1746952740200, "results": "97", "hashOfConfig": "96"}, {"size": 996, "mtime": 1746970152489, "results": "98", "hashOfConfig": "96"}, {"size": 10788, "mtime": 1746864244183, "results": "99", "hashOfConfig": "96"}, {"size": 18405, "mtime": 1748034045686, "results": "100", "hashOfConfig": "96"}, {"size": 6001, "mtime": 1748200429907, "results": "101", "hashOfConfig": "96"}, {"size": 2216, "mtime": 1746640055487, "results": "102", "hashOfConfig": "96"}, {"size": 7394, "mtime": 1748034003517, "results": "103", "hashOfConfig": "96"}, {"size": 6749, "mtime": 1746282201800, "results": "104", "hashOfConfig": "96"}, {"size": 21982, "mtime": 1748068137555, "results": "105", "hashOfConfig": "96"}, {"size": 2535, "mtime": 1746647873596, "results": "106", "hashOfConfig": "96"}, {"size": 2050, "mtime": 1746647945415, "results": "107", "hashOfConfig": "96"}, {"size": 700, "mtime": 1747545501078, "results": "108", "hashOfConfig": "96"}, {"size": 17518, "mtime": 1748664526035, "results": "109", "hashOfConfig": "96"}, {"size": 3999, "mtime": 1746943038491, "results": "110", "hashOfConfig": "96"}, {"size": 4001, "mtime": 1748121689116, "results": "111", "hashOfConfig": "96"}, {"size": 1630, "mtime": 1746336079554, "results": "112", "hashOfConfig": "96"}, {"size": 3070, "mtime": 1746637986362, "results": "113", "hashOfConfig": "96"}, {"size": 37999, "mtime": 1748666433118, "results": "114", "hashOfConfig": "96"}, {"size": 324, "mtime": 1748675785146, "results": "115", "hashOfConfig": "96"}, {"size": 9068, "mtime": 1746856425683, "results": "116", "hashOfConfig": "96"}, {"size": 2210, "mtime": 1747432283057, "results": "117", "hashOfConfig": "96"}, {"size": 4494, "mtime": 1748121063631, "results": "118", "hashOfConfig": "96"}, {"size": 69835, "mtime": 1748670082686, "results": "119", "hashOfConfig": "96"}, {"size": 3969, "mtime": 1746864496870, "results": "120", "hashOfConfig": "96"}, {"size": 3609, "mtime": 1746944025177, "results": "121", "hashOfConfig": "96"}, {"size": 4142, "mtime": 1746942978805, "results": "122", "hashOfConfig": "96"}, {"size": 3986, "mtime": 1746864510624, "results": "123", "hashOfConfig": "96"}, {"size": 3973, "mtime": 1746864489032, "results": "124", "hashOfConfig": "96"}, {"size": 2975, "mtime": 1747554796402, "results": "125", "hashOfConfig": "96"}, {"size": 3429, "mtime": 1747721794176, "results": "126", "hashOfConfig": "96"}, {"size": 3109, "mtime": 1747824114392, "results": "127", "hashOfConfig": "96"}, {"size": 2929, "mtime": 1747655572696, "results": "128", "hashOfConfig": "96"}, {"size": 3302, "mtime": 1748000902435, "results": "129", "hashOfConfig": "96"}, {"size": 5597, "mtime": 1748070089791, "results": "130", "hashOfConfig": "96"}, {"size": 5880, "mtime": 1748121404574, "results": "131", "hashOfConfig": "96"}, {"size": 3889, "mtime": 1748664890350, "results": "132", "hashOfConfig": "96"}, {"size": 4720, "mtime": 1746771178920, "results": "133", "hashOfConfig": "96"}, {"size": 7121, "mtime": 1746281148395, "results": "134", "hashOfConfig": "96"}, {"size": 7958, "mtime": 1746280443400, "results": "135", "hashOfConfig": "96"}, {"size": 6259, "mtime": 1746965906057, "results": "136", "hashOfConfig": "96"}, {"size": 4215, "mtime": 1746278746358, "results": "137", "hashOfConfig": "96"}, {"size": 1273, "mtime": 1746809069006, "results": "138", "hashOfConfig": "96"}, {"size": 14270, "mtime": 1748371983481, "results": "139", "hashOfConfig": "96"}, {"size": 2752, "mtime": 1747022186740, "results": "140", "hashOfConfig": "96"}, {"size": 1072, "mtime": 1746637929350, "results": "141", "hashOfConfig": "96"}, {"size": 6745, "mtime": 1747545492454, "results": "142", "hashOfConfig": "96"}, {"size": 38569, "mtime": 1748371531457, "results": "143", "hashOfConfig": "96"}, {"size": 23333, "mtime": 1746463652843, "results": "144", "hashOfConfig": "96"}, {"size": 47271, "mtime": 1748072224692, "results": "145", "hashOfConfig": "96"}, {"size": 38669, "mtime": 1748199713253, "results": "146", "hashOfConfig": "96"}, {"size": 1947, "mtime": 1748120984640, "results": "147", "hashOfConfig": "96"}, {"size": 54895, "mtime": 1748370360136, "results": "148", "hashOfConfig": "96"}, {"size": 14635, "mtime": 1748666301849, "results": "149", "hashOfConfig": "96"}, {"size": 8538, "mtime": 1748666450738, "results": "150", "hashOfConfig": "96"}, {"size": 11771, "mtime": 1746948731812, "results": "151", "hashOfConfig": "96"}, {"size": 4056, "mtime": 1748067890376, "results": "152", "hashOfConfig": "96"}, {"size": 9215, "mtime": 1748668814050, "results": "153", "hashOfConfig": "96"}, {"size": 10993, "mtime": 1747154871546, "results": "154", "hashOfConfig": "96"}, {"size": 12150, "mtime": 1748205557322, "results": "155", "hashOfConfig": "96"}, {"size": 23685, "mtime": 1748676563925, "results": "156", "hashOfConfig": "96"}, {"size": 8016, "mtime": 1748205755456, "results": "157", "hashOfConfig": "96"}, {"size": 12306, "mtime": 1748205594594, "results": "158", "hashOfConfig": "96"}, {"size": 7032, "mtime": 1748069273238, "results": "159", "hashOfConfig": "96"}, {"size": 8589, "mtime": 1748207111023, "results": "160", "hashOfConfig": "96"}, {"size": 9979, "mtime": 1748069243848, "results": "161", "hashOfConfig": "96"}, {"size": 10821, "mtime": 1748069202177, "results": "162", "hashOfConfig": "96"}, {"size": 36555, "mtime": 1747684003188, "results": "163", "hashOfConfig": "96"}, {"size": 9483, "mtime": 1747194869458, "results": "164", "hashOfConfig": "96"}, {"size": 13900, "mtime": 1748182219170, "results": "165", "hashOfConfig": "96"}, {"size": 48588, "mtime": 1747948123233, "results": "166", "hashOfConfig": "96"}, {"size": 92270, "mtime": 1748123070273, "results": "167", "hashOfConfig": "96"}, {"size": 522, "mtime": 1747022186711, "results": "168", "hashOfConfig": "96"}, {"size": 6612, "mtime": 1748069456201, "results": "169", "hashOfConfig": "96"}, {"size": 3796, "mtime": 1747022186720, "results": "170", "hashOfConfig": "96"}, {"size": 1703, "mtime": 1746972529152, "results": "171", "hashOfConfig": "96"}, {"size": 19892, "mtime": 1747554544219, "results": "172", "hashOfConfig": "96"}, {"size": 12050, "mtime": 1747547543421, "results": "173", "hashOfConfig": "96"}, {"size": 1686, "mtime": 1746946499500, "results": "174", "hashOfConfig": "96"}, {"size": 5145, "mtime": 1746914029633, "results": "175", "hashOfConfig": "96"}, {"size": 9788, "mtime": 1747491601484, "results": "176", "hashOfConfig": "96"}, {"size": 22179, "mtime": 1747432554979, "results": "177", "hashOfConfig": "96"}, {"size": 2258, "mtime": 1746946368534, "results": "178", "hashOfConfig": "96"}, {"size": 4094, "mtime": 1748161663641, "results": "179", "hashOfConfig": "96"}, {"size": 5273, "mtime": 1747946737459, "results": "180", "hashOfConfig": "96"}, {"size": 4346, "mtime": 1747491472989, "results": "181", "hashOfConfig": "96"}, {"size": 15571, "mtime": 1747980774491, "results": "182", "hashOfConfig": "96"}, {"size": 7839, "mtime": 1748664451344, "results": "183", "hashOfConfig": "96"}, {"size": 6529, "mtime": 1748664406267, "results": "184", "hashOfConfig": "96"}, {"size": 15739, "mtime": 1748664968476, "results": "185", "hashOfConfig": "96"}, {"size": 6448, "mtime": 1748664917658, "results": "186", "hashOfConfig": "96"}, {"size": 5536, "mtime": 1748670096009, "results": "187", "hashOfConfig": "96"}, {"size": 5457, "mtime": 1748666884369, "results": "188", "hashOfConfig": "96"}, {"size": 5605, "mtime": 1748666925194, "results": "189", "hashOfConfig": "96"}, {"filePath": "190", "messages": "191", "suppressedMessages": "192", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "1f0jzw9", {"filePath": "193", "messages": "194", "suppressedMessages": "195", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "196", "messages": "197", "suppressedMessages": "198", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "199", "messages": "200", "suppressedMessages": "201", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "202", "messages": "203", "suppressedMessages": "204", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "205", "messages": "206", "suppressedMessages": "207", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "208", "messages": "209", "suppressedMessages": "210", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "211", "messages": "212", "suppressedMessages": "213", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 4, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "214", "messages": "215", "suppressedMessages": "216", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "217", "messages": "218", "suppressedMessages": "219", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 10, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "220", "messages": "221", "suppressedMessages": "222", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "223", "messages": "224", "suppressedMessages": "225", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 10, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "226", "messages": "227", "suppressedMessages": "228", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "229", "messages": "230", "suppressedMessages": "231", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "232", "messages": "233", "suppressedMessages": "234", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 15, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "235", "messages": "236", "suppressedMessages": "237", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 4, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "238", "messages": "239", "suppressedMessages": "240", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "241", "messages": "242", "suppressedMessages": "243", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "244", "messages": "245", "suppressedMessages": "246", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 15, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "247", "messages": "248", "suppressedMessages": "249", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "250", "messages": "251", "suppressedMessages": "252", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "253", "messages": "254", "suppressedMessages": "255", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "256", "messages": "257", "suppressedMessages": "258", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "259", "messages": "260", "suppressedMessages": "261", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 12, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "262", "messages": "263", "suppressedMessages": "264", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "265", "messages": "266", "suppressedMessages": "267", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "268", "messages": "269", "suppressedMessages": "270", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "271", "messages": "272", "suppressedMessages": "273", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "274", "messages": "275", "suppressedMessages": "276", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "277", "messages": "278", "suppressedMessages": "279", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "280", "messages": "281", "suppressedMessages": "282", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 7, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "283", "messages": "284", "suppressedMessages": "285", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 3, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "286", "messages": "287", "suppressedMessages": "288", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "289", "messages": "290", "suppressedMessages": "291", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 7, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "292", "messages": "293", "suppressedMessages": "294", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "295", "messages": "296", "suppressedMessages": "297", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "298", "messages": "299", "suppressedMessages": "300", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "301", "messages": "302", "suppressedMessages": "303", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "304", "messages": "305", "suppressedMessages": "306", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "307", "messages": "308", "suppressedMessages": "309", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "310", "messages": "311", "suppressedMessages": "312", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "313", "messages": "314", "suppressedMessages": "315", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "316", "messages": "317", "suppressedMessages": "318", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "319", "messages": "320", "suppressedMessages": "321", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "322", "messages": "323", "suppressedMessages": "324", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "325", "messages": "326", "suppressedMessages": "327", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "328", "messages": "329", "suppressedMessages": "330", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "331", "messages": "332", "suppressedMessages": "333", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 8, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "334", "messages": "335", "suppressedMessages": "336", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 12, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "337", "messages": "338", "suppressedMessages": "339", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 14, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "340", "messages": "341", "suppressedMessages": "342", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 7, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "343", "messages": "344", "suppressedMessages": "345", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "346", "messages": "347", "suppressedMessages": "348", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 23, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "349", "messages": "350", "suppressedMessages": "351", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "352", "messages": "353", "suppressedMessages": "354", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "355", "messages": "356", "suppressedMessages": "357", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "358", "messages": "359", "suppressedMessages": "360", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "361", "messages": "362", "suppressedMessages": "363", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 11, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "364", "messages": "365", "suppressedMessages": "366", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "367", "messages": "368", "suppressedMessages": "369", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "370", "messages": "371", "suppressedMessages": "372", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 22, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "373", "messages": "374", "suppressedMessages": "375", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 4, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "376", "messages": "377", "suppressedMessages": "378", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "379", "messages": "380", "suppressedMessages": "381", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "382", "messages": "383", "suppressedMessages": "384", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "385", "messages": "386", "suppressedMessages": "387", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "388", "messages": "389", "suppressedMessages": "390", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "391", "messages": "392", "suppressedMessages": "393", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 11, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "394", "messages": "395", "suppressedMessages": "396", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "397", "messages": "398", "suppressedMessages": "399", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 4, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "400", "messages": "401", "suppressedMessages": "402", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 24, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "403", "messages": "404", "suppressedMessages": "405", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 16, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "406", "messages": "407", "suppressedMessages": "408", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "409", "messages": "410", "suppressedMessages": "411", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "412", "messages": "413", "suppressedMessages": "414", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "415", "messages": "416", "suppressedMessages": "417", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "418", "messages": "419", "suppressedMessages": "420", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 20, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "421", "messages": "422", "suppressedMessages": "423", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "424", "messages": "425", "suppressedMessages": "426", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "427", "messages": "428", "suppressedMessages": "429", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "430", "messages": "431", "suppressedMessages": "432", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "433", "messages": "434", "suppressedMessages": "435", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 8, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "436", "messages": "437", "suppressedMessages": "438", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "439", "messages": "440", "suppressedMessages": "441", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "442", "messages": "443", "suppressedMessages": "444", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "445", "messages": "446", "suppressedMessages": "447", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "448", "messages": "449", "suppressedMessages": "450", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 3, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "451", "messages": "452", "suppressedMessages": "453", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "454", "messages": "455", "suppressedMessages": "456", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "457", "messages": "458", "suppressedMessages": "459", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "460", "messages": "461", "suppressedMessages": "462", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "463", "messages": "464", "suppressedMessages": "465", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "466", "messages": "467", "suppressedMessages": "468", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "469", "messages": "470", "suppressedMessages": "471", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, "C:\\CMS\\webapp\\frontend\\src\\index.js", [], [], "C:\\CMS\\webapp\\frontend\\src\\App.js", [], [], "C:\\CMS\\webapp\\frontend\\src\\context\\GlobalContext.js", [], [], "C:\\CMS\\webapp\\frontend\\src\\context\\AuthContext.js", [], [], "C:\\CMS\\webapp\\frontend\\src\\pages\\LoginPageNew.js", [], [], "C:\\CMS\\webapp\\frontend\\src\\pages\\Dashboard.js", [], [], "C:\\CMS\\webapp\\frontend\\src\\components\\ProtectedRoute.js", [], [], "C:\\CMS\\webapp\\frontend\\src\\services\\authService.js", ["472", "473", "474", "475"], [], "C:\\CMS\\webapp\\frontend\\src\\pages\\AdminPage.js", [], [], "C:\\CMS\\webapp\\frontend\\src\\components\\TopNavbar.js", ["476", "477", "478", "479", "480", "481", "482", "483", "484", "485"], [], "C:\\CMS\\webapp\\frontend\\src\\pages\\CaviPage.js", ["486"], [], "C:\\CMS\\webapp\\frontend\\src\\pages\\HomePage.js", ["487", "488", "489", "490", "491", "492", "493", "494", "495", "496"], [], "C:\\CMS\\webapp\\frontend\\src\\pages\\TestBobinePage.js", [], [], "C:\\CMS\\webapp\\frontend\\src\\pages\\UserPage.js", [], [], "C:\\CMS\\webapp\\frontend\\src\\pages\\cavi\\ParcoCaviPage.js", ["497", "498", "499", "500", "501", "502", "503", "504", "505", "506", "507", "508", "509", "510", "511"], [], "C:\\CMS\\webapp\\frontend\\src\\pages\\cavi\\CertificazioneCaviPage.js", ["512", "513", "514", "515"], [], "C:\\CMS\\webapp\\frontend\\src\\components\\admin\\UserExpirationChecker.js", ["516"], [], "C:\\CMS\\webapp\\frontend\\src\\pages\\cavi\\GestioneComandeePage.js", ["517", "518"], [], "C:\\CMS\\webapp\\frontend\\src\\pages\\cavi\\VisualizzaCaviPage.js", ["519", "520", "521", "522", "523", "524", "525", "526", "527", "528", "529", "530", "531", "532", "533"], [], "C:\\CMS\\webapp\\frontend\\src\\config.js", [], [], "C:\\CMS\\webapp\\frontend\\src\\pages\\cavi\\TestCaviPage.js", ["534"], [], "C:\\CMS\\webapp\\frontend\\src\\services\\axiosConfig.js", [], [], "C:\\CMS\\webapp\\frontend\\src\\pages\\CertificazioniPageDebug.jsx", ["535"], [], "C:\\CMS\\webapp\\frontend\\src\\pages\\cavi\\ReportCaviPageNew.js", ["536", "537", "538", "539", "540", "541", "542", "543", "544", "545", "546", "547"], [], "C:\\CMS\\webapp\\frontend\\src\\pages\\cavi\\parco\\EliminaBobinaPage.js", ["548", "549"], [], "C:\\CMS\\webapp\\frontend\\src\\pages\\cavi\\parco\\VisualizzaBobinePage.js", [], [], "C:\\CMS\\webapp\\frontend\\src\\pages\\cavi\\parco\\CreaBobinaPage.js", [], [], "C:\\CMS\\webapp\\frontend\\src\\pages\\cavi\\parco\\StoricoUtilizzoPage.js", ["550", "551"], [], "C:\\CMS\\webapp\\frontend\\src\\pages\\cavi\\parco\\ModificaBobinaPage.js", ["552", "553"], [], "C:\\CMS\\webapp\\frontend\\src\\pages\\cavi\\posa\\MetriPosatiSemplificatoPage.js", ["554"], [], "C:\\CMS\\webapp\\frontend\\src\\pages\\cavi\\posa\\ModificaBobinaPage.js", ["555", "556", "557", "558", "559", "560", "561"], [], "C:\\CMS\\webapp\\frontend\\src\\pages\\cavi\\posa\\ModificaCavoPage.js", ["562", "563", "564"], [], "C:\\CMS\\webapp\\frontend\\src\\pages\\cavi\\posa\\InserisciMetriPage.js", [], [], "C:\\CMS\\webapp\\frontend\\src\\pages\\cavi\\posa\\CollegamentiPage.js", ["565", "566", "567", "568", "569", "570", "571"], [], "C:\\CMS\\webapp\\frontend\\src\\pages\\cantieri\\CantierePage.js", ["572", "573"], [], "C:\\CMS\\webapp\\frontend\\src\\services\\excelService.js", [], [], "C:\\CMS\\webapp\\frontend\\src\\services\\cantieriService.js", ["574", "575"], [], "C:\\CMS\\webapp\\frontend\\src\\components\\admin\\ImpersonateUser.js", [], [], "C:\\CMS\\webapp\\frontend\\src\\components\\admin\\UsersList.js", [], [], "C:\\CMS\\webapp\\frontend\\src\\components\\admin\\UserForm.js", [], [], "C:\\CMS\\webapp\\frontend\\src\\components\\admin\\DatabaseView.js", [], [], "C:\\CMS\\webapp\\frontend\\src\\components\\admin\\ResetDatabase.js", [], [], "C:\\CMS\\webapp\\frontend\\src\\components\\common\\SelectedCantiereDisplay.js", [], [], "C:\\CMS\\webapp\\frontend\\src\\components\\cavi\\ExcelPopup.js", ["576", "577"], [], "C:\\CMS\\webapp\\frontend\\src\\services\\userService.js", ["578", "579"], [], "C:\\CMS\\webapp\\frontend\\src\\components\\common\\AdminHomeButton.js", [], [], "C:\\CMS\\webapp\\frontend\\src\\components\\cavi\\TestBobineComponent.js", [], [], "C:\\CMS\\webapp\\frontend\\src\\components\\cavi\\CertificazioneCavi.js", ["580", "581", "582", "583", "584", "585", "586", "587"], [], "C:\\CMS\\webapp\\frontend\\src\\components\\cavi\\GestioneComande.js", ["588", "589", "590", "591", "592", "593", "594", "595", "596", "597", "598", "599"], [], "C:\\CMS\\webapp\\frontend\\src\\components\\cavi\\ParcoCavi.js", ["600", "601", "602", "603", "604", "605", "606", "607", "608", "609", "610", "611", "612", "613"], [], "C:\\CMS\\webapp\\frontend\\src\\components\\cavi\\PosaCaviCollegamenti.js", ["614", "615", "616", "617", "618", "619", "620"], ["621"], "C:\\CMS\\webapp\\frontend\\src\\services\\apiService.js", [], [], "C:\\CMS\\webapp\\frontend\\src\\services\\caviService.js", ["622", "623", "624", "625", "626", "627", "628", "629", "630", "631", "632", "633", "634", "635", "636", "637", "638", "639", "640", "641", "642", "643", "644"], [], "C:\\CMS\\webapp\\frontend\\src\\components\\cavi\\CavoForm.js", ["645", "646"], [], "C:\\CMS\\webapp\\frontend\\src\\components\\cavi\\CaviFilterableTable.js", [], [], "C:\\CMS\\webapp\\frontend\\src\\components\\common\\FilterableTable.js", [], [], "C:\\CMS\\webapp\\frontend\\src\\services\\reportService.js", ["647", "648"], [], "C:\\CMS\\webapp\\frontend\\src\\components\\charts\\ProgressChart.js", ["649", "650", "651", "652", "653", "654", "655", "656", "657", "658", "659"], [], "C:\\CMS\\webapp\\frontend\\src\\utils\\validationUtils.js", [], [], "C:\\CMS\\webapp\\frontend\\src\\components\\charts\\TimelineChart.js", ["660"], [], "C:\\CMS\\webapp\\frontend\\src\\components\\charts\\BoqChart.js", ["661", "662", "663", "664", "665", "666", "667", "668", "669", "670", "671", "672", "673", "674", "675", "676", "677", "678", "679", "680", "681", "682"], [], "C:\\CMS\\webapp\\frontend\\src\\components\\charts\\BobineChart.js", ["683", "684", "685", "686"], [], "C:\\CMS\\webapp\\frontend\\src\\components\\charts\\CaviStatoChart.js", ["687", "688"], [], "C:\\CMS\\webapp\\frontend\\src\\components\\certificazioni\\StrumentiList.jsx", [], [], "C:\\CMS\\webapp\\frontend\\src\\components\\certificazioni\\StrumentoForm.jsx", ["689"], [], "C:\\CMS\\webapp\\frontend\\src\\components\\certificazioni\\CertificazioneForm.jsx", ["690"], [], "C:\\CMS\\webapp\\frontend\\src\\components\\certificazioni\\CertificazioniList.jsx", ["691"], [], "C:\\CMS\\webapp\\frontend\\src\\components\\cavi\\MetriPosatiSemplificatoForm.js", ["692", "693", "694", "695", "696", "697", "698", "699", "700", "701", "702"], [], "C:\\CMS\\webapp\\frontend\\src\\components\\cavi\\SelezionaCavoForm.js", ["703"], [], "C:\\CMS\\webapp\\frontend\\src\\components\\cavi\\CollegamentiCavo.js", ["704", "705", "706", "707"], [], "C:\\CMS\\webapp\\frontend\\src\\components\\cavi\\ModificaBobinaForm.js", ["708", "709", "710", "711", "712", "713", "714", "715", "716", "717", "718", "719", "720", "721", "722", "723", "724", "725", "726", "727", "728", "729", "730", "731"], [], "C:\\CMS\\webapp\\frontend\\src\\components\\cavi\\InserisciMetriForm.js", ["732", "733", "734", "735", "736", "737", "738", "739", "740", "741", "742", "743", "744", "745", "746", "747"], [], "C:\\CMS\\webapp\\frontend\\src\\services\\adminService.js", ["748", "749"], [], "C:\\CMS\\webapp\\frontend\\src\\services\\certificazioneService.js", ["750", "751"], [], "C:\\CMS\\webapp\\frontend\\src\\services\\comandeService.js", ["752", "753"], [], "C:\\CMS\\webapp\\frontend\\src\\utils\\navigationUtils.js", [], [], "C:\\CMS\\webapp\\frontend\\src\\services\\parcoCaviService.js", ["754", "755", "756", "757", "758", "759", "760", "761", "762", "763", "764", "765", "766", "767", "768", "769", "770", "771", "772", "773"], [], "C:\\CMS\\webapp\\frontend\\src\\utils\\bobinaValidationUtils.js", [], [], "C:\\CMS\\webapp\\frontend\\src\\utils\\dateUtils.js", [], [], "C:\\CMS\\webapp\\frontend\\src\\components\\cavi\\ConfigurazioneDialog.js", [], [], "C:\\CMS\\webapp\\frontend\\src\\components\\cavi\\BobineFilterableTable.js", [], [], "C:\\CMS\\webapp\\frontend\\src\\components\\cavi\\QuickAddCablesDialog.js", ["774", "775", "776", "777", "778", "779", "780", "781"], [], "C:\\CMS\\webapp\\frontend\\src\\components\\common\\FilterableTableHeader.js", [], [], "C:\\CMS\\webapp\\frontend\\src\\utils\\stateUtils.js", [], [], "C:\\CMS\\webapp\\frontend\\src\\components\\cavi\\IncompatibleReelDialog.js", ["782"], [], "C:\\CMS\\webapp\\frontend\\src\\components\\cavi\\CavoDetailsView.js", [], [], "C:\\CMS\\webapp\\frontend\\src\\components\\common\\ExcelLikeFilter.js", ["783", "784", "785"], [], "C:\\CMS\\webapp\\frontend\\src\\components\\cantieri\\CantieriFilterableTable.js", ["786"], [], "C:\\CMS\\webapp\\frontend\\src\\components\\cantieri\\EditCantiereDialog.js", ["787"], [], "C:\\CMS\\webapp\\frontend\\src\\components\\cantieri\\PasswordManagementDialog.js", [], [], "C:\\CMS\\webapp\\frontend\\src\\components\\cantieri\\HoldToViewButton.js", [], [], "C:\\CMS\\webapp\\frontend\\src\\components\\common\\MetricCard.js", [], [], "C:\\CMS\\webapp\\frontend\\src\\components\\common\\EmptyState.js", ["788"], [], "C:\\CMS\\webapp\\frontend\\src\\components\\common\\ReportSection.js", ["789"], [], {"ruleId": "790", "severity": 1, "message": "791", "line": 78, "column": 11, "nodeType": "792", "messageId": "793", "endLine": 78, "endColumn": 115}, {"ruleId": "790", "severity": 1, "message": "791", "line": 80, "column": 11, "nodeType": "792", "messageId": "793", "endLine": 80, "endColumn": 107}, {"ruleId": "790", "severity": 1, "message": "791", "line": 86, "column": 9, "nodeType": "792", "messageId": "793", "endLine": 86, "endColumn": 105}, {"ruleId": "790", "severity": 1, "message": "791", "line": 89, "column": 9, "nodeType": "792", "messageId": "793", "endLine": 89, "endColumn": 41}, {"ruleId": "794", "severity": 1, "message": "795", "line": 13, "column": 3, "nodeType": "796", "messageId": "797", "endLine": 13, "endColumn": 9}, {"ruleId": "794", "severity": 1, "message": "798", "line": 20, "column": 25, "nodeType": "796", "messageId": "797", "endLine": 20, "endColumn": 34}, {"ruleId": "794", "severity": 1, "message": "799", "line": 21, "column": 19, "nodeType": "796", "messageId": "797", "endLine": 21, "endColumn": 35}, {"ruleId": "794", "severity": 1, "message": "800", "line": 22, "column": 12, "nodeType": "796", "messageId": "797", "endLine": 22, "endColumn": 21}, {"ruleId": "794", "severity": 1, "message": "801", "line": 23, "column": 18, "nodeType": "796", "messageId": "797", "endLine": 23, "endColumn": 28}, {"ruleId": "794", "severity": 1, "message": "802", "line": 56, "column": 10, "nodeType": "796", "messageId": "797", "endLine": 56, "endColumn": 22}, {"ruleId": "794", "severity": 1, "message": "803", "line": 57, "column": 10, "nodeType": "796", "messageId": "797", "endLine": 57, "endColumn": 23}, {"ruleId": "794", "severity": 1, "message": "804", "line": 58, "column": 10, "nodeType": "796", "messageId": "797", "endLine": 58, "endColumn": 26}, {"ruleId": "794", "severity": 1, "message": "805", "line": 59, "column": 10, "nodeType": "796", "messageId": "797", "endLine": 59, "endColumn": 22}, {"ruleId": "794", "severity": 1, "message": "806", "line": 68, "column": 9, "nodeType": "796", "messageId": "797", "endLine": 68, "endColumn": 29}, {"ruleId": "794", "severity": 1, "message": "807", "line": 1, "column": 8, "nodeType": "796", "messageId": "797", "endLine": 1, "endColumn": 13}, {"ruleId": "794", "severity": 1, "message": "808", "line": 2, "column": 27, "nodeType": "796", "messageId": "797", "endLine": 2, "endColumn": 31}, {"ruleId": "794", "severity": 1, "message": "809", "line": 2, "column": 33, "nodeType": "796", "messageId": "797", "endLine": 2, "endColumn": 37}, {"ruleId": "794", "severity": 1, "message": "810", "line": 2, "column": 39, "nodeType": "796", "messageId": "797", "endLine": 2, "endColumn": 50}, {"ruleId": "794", "severity": 1, "message": "811", "line": 2, "column": 52, "nodeType": "796", "messageId": "797", "endLine": 2, "endColumn": 66}, {"ruleId": "794", "severity": 1, "message": "795", "line": 2, "column": 68, "nodeType": "796", "messageId": "797", "endLine": 2, "endColumn": 74}, {"ruleId": "794", "severity": 1, "message": "798", "line": 5, "column": 25, "nodeType": "796", "messageId": "797", "endLine": 5, "endColumn": 34}, {"ruleId": "794", "severity": 1, "message": "799", "line": 6, "column": 19, "nodeType": "796", "messageId": "797", "endLine": 6, "endColumn": 35}, {"ruleId": "794", "severity": 1, "message": "800", "line": 7, "column": 12, "nodeType": "796", "messageId": "797", "endLine": 7, "endColumn": 21}, {"ruleId": "794", "severity": 1, "message": "801", "line": 8, "column": 18, "nodeType": "796", "messageId": "797", "endLine": 8, "endColumn": 28}, {"ruleId": "794", "severity": 1, "message": "812", "line": 43, "column": 9, "nodeType": "796", "messageId": "797", "endLine": 43, "endColumn": 19}, {"ruleId": "794", "severity": 1, "message": "809", "line": 9, "column": 3, "nodeType": "796", "messageId": "797", "endLine": 9, "endColumn": 7}, {"ruleId": "794", "severity": 1, "message": "810", "line": 10, "column": 3, "nodeType": "796", "messageId": "797", "endLine": 10, "endColumn": 14}, {"ruleId": "794", "severity": 1, "message": "813", "line": 11, "column": 3, "nodeType": "796", "messageId": "797", "endLine": 11, "endColumn": 14}, {"ruleId": "794", "severity": 1, "message": "808", "line": 12, "column": 3, "nodeType": "796", "messageId": "797", "endLine": 12, "endColumn": 7}, {"ruleId": "794", "severity": 1, "message": "814", "line": 13, "column": 3, "nodeType": "796", "messageId": "797", "endLine": 13, "endColumn": 10}, {"ruleId": "794", "severity": 1, "message": "815", "line": 18, "column": 11, "nodeType": "796", "messageId": "797", "endLine": 18, "endColumn": 19}, {"ruleId": "794", "severity": 1, "message": "816", "line": 19, "column": 15, "nodeType": "796", "messageId": "797", "endLine": 19, "endColumn": 27}, {"ruleId": "794", "severity": 1, "message": "817", "line": 20, "column": 10, "nodeType": "796", "messageId": "797", "endLine": 20, "endColumn": 17}, {"ruleId": "794", "severity": 1, "message": "818", "line": 21, "column": 11, "nodeType": "796", "messageId": "797", "endLine": 21, "endColumn": 19}, {"ruleId": "794", "severity": 1, "message": "819", "line": 22, "column": 13, "nodeType": "796", "messageId": "797", "endLine": 22, "endColumn": 23}, {"ruleId": "794", "severity": 1, "message": "820", "line": 23, "column": 14, "nodeType": "796", "messageId": "797", "endLine": 23, "endColumn": 25}, {"ruleId": "794", "severity": 1, "message": "821", "line": 28, "column": 8, "nodeType": "796", "messageId": "797", "endLine": 28, "endColumn": 17}, {"ruleId": "794", "severity": 1, "message": "822", "line": 31, "column": 11, "nodeType": "796", "messageId": "797", "endLine": 31, "endColumn": 26}, {"ruleId": "794", "severity": 1, "message": "823", "line": 51, "column": 9, "nodeType": "796", "messageId": "797", "endLine": 51, "endColumn": 22}, {"ruleId": "794", "severity": 1, "message": "824", "line": 56, "column": 9, "nodeType": "796", "messageId": "797", "endLine": 56, "endColumn": 20}, {"ruleId": "794", "severity": 1, "message": "825", "line": 5, "column": 3, "nodeType": "796", "messageId": "797", "endLine": 5, "endColumn": 8}, {"ruleId": "794", "severity": 1, "message": "815", "line": 13, "column": 11, "nodeType": "796", "messageId": "797", "endLine": 13, "endColumn": 19}, {"ruleId": "794", "severity": 1, "message": "822", "line": 21, "column": 11, "nodeType": "796", "messageId": "797", "endLine": 21, "endColumn": 26}, {"ruleId": "794", "severity": 1, "message": "826", "line": 28, "column": 9, "nodeType": "796", "messageId": "797", "endLine": 28, "endColumn": 21}, {"ruleId": "794", "severity": 1, "message": "827", "line": 11, "column": 10, "nodeType": "796", "messageId": "797", "endLine": 11, "endColumn": 19}, {"ruleId": "794", "severity": 1, "message": "815", "line": 13, "column": 11, "nodeType": "796", "messageId": "797", "endLine": 13, "endColumn": 19}, {"ruleId": "794", "severity": 1, "message": "822", "line": 21, "column": 11, "nodeType": "796", "messageId": "797", "endLine": 21, "endColumn": 26}, {"ruleId": "794", "severity": 1, "message": "809", "line": 8, "column": 3, "nodeType": "796", "messageId": "797", "endLine": 8, "endColumn": 7}, {"ruleId": "794", "severity": 1, "message": "810", "line": 9, "column": 3, "nodeType": "796", "messageId": "797", "endLine": 9, "endColumn": 14}, {"ruleId": "794", "severity": 1, "message": "828", "line": 11, "column": 3, "nodeType": "796", "messageId": "797", "endLine": 11, "endColumn": 13}, {"ruleId": "794", "severity": 1, "message": "829", "line": 14, "column": 3, "nodeType": "796", "messageId": "797", "endLine": 14, "endColumn": 17}, {"ruleId": "794", "severity": 1, "message": "830", "line": 25, "column": 8, "nodeType": "796", "messageId": "797", "endLine": 25, "endColumn": 16}, {"ruleId": "794", "severity": 1, "message": "831", "line": 31, "column": 8, "nodeType": "796", "messageId": "797", "endLine": 31, "endColumn": 16}, {"ruleId": "794", "severity": 1, "message": "822", "line": 37, "column": 11, "nodeType": "796", "messageId": "797", "endLine": 37, "endColumn": 26}, {"ruleId": "794", "severity": 1, "message": "832", "line": 39, "column": 9, "nodeType": "796", "messageId": "797", "endLine": 39, "endColumn": 17}, {"ruleId": "794", "severity": 1, "message": "826", "line": 41, "column": 10, "nodeType": "796", "messageId": "797", "endLine": 41, "endColumn": 22}, {"ruleId": "794", "severity": 1, "message": "833", "line": 109, "column": 19, "nodeType": "796", "messageId": "797", "endLine": 109, "endColumn": 29}, {"ruleId": "794", "severity": 1, "message": "834", "line": 117, "column": 10, "nodeType": "796", "messageId": "797", "endLine": 117, "endColumn": 28}, {"ruleId": "794", "severity": 1, "message": "835", "line": 118, "column": 10, "nodeType": "796", "messageId": "797", "endLine": 118, "endColumn": 23}, {"ruleId": "794", "severity": 1, "message": "836", "line": 118, "column": 25, "nodeType": "796", "messageId": "797", "endLine": 118, "endColumn": 41}, {"ruleId": "837", "severity": 1, "message": "838", "line": 472, "column": 6, "nodeType": "839", "endLine": 472, "endColumn": 15, "suggestions": "840"}, {"ruleId": "794", "severity": 1, "message": "841", "line": 477, "column": 9, "nodeType": "796", "messageId": "797", "endLine": 477, "endColumn": 26}, {"ruleId": "794", "severity": 1, "message": "842", "line": 1, "column": 27, "nodeType": "796", "messageId": "797", "endLine": 1, "endColumn": 36}, {"ruleId": "794", "severity": 1, "message": "843", "line": 49, "column": 19, "nodeType": "796", "messageId": "797", "endLine": 49, "endColumn": 26}, {"ruleId": "794", "severity": 1, "message": "813", "line": 10, "column": 3, "nodeType": "796", "messageId": "797", "endLine": 10, "endColumn": 14}, {"ruleId": "794", "severity": 1, "message": "828", "line": 16, "column": 3, "nodeType": "796", "messageId": "797", "endLine": 16, "endColumn": 13}, {"ruleId": "794", "severity": 1, "message": "844", "line": 17, "column": 3, "nodeType": "796", "messageId": "797", "endLine": 17, "endColumn": 10}, {"ruleId": "794", "severity": 1, "message": "845", "line": 36, "column": 15, "nodeType": "796", "messageId": "797", "endLine": 36, "endColumn": 27}, {"ruleId": "794", "severity": 1, "message": "846", "line": 42, "column": 16, "nodeType": "796", "messageId": "797", "endLine": 42, "endColumn": 29}, {"ruleId": "794", "severity": 1, "message": "847", "line": 56, "column": 8, "nodeType": "796", "messageId": "797", "endLine": 56, "endColumn": 21}, {"ruleId": "794", "severity": 1, "message": "848", "line": 63, "column": 8, "nodeType": "796", "messageId": "797", "endLine": 63, "endColumn": 22}, {"ruleId": "794", "severity": 1, "message": "832", "line": 66, "column": 9, "nodeType": "796", "messageId": "797", "endLine": 66, "endColumn": 17}, {"ruleId": "794", "severity": 1, "message": "849", "line": 68, "column": 11, "nodeType": "796", "messageId": "797", "endLine": 68, "endColumn": 15}, {"ruleId": "794", "severity": 1, "message": "850", "line": 166, "column": 9, "nodeType": "796", "messageId": "797", "endLine": 166, "endColumn": 20}, {"ruleId": "794", "severity": 1, "message": "851", "line": 284, "column": 9, "nodeType": "796", "messageId": "797", "endLine": 284, "endColumn": 27}, {"ruleId": "794", "severity": 1, "message": "852", "line": 326, "column": 9, "nodeType": "796", "messageId": "797", "endLine": 326, "endColumn": 28}, {"ruleId": "794", "severity": 1, "message": "825", "line": 5, "column": 3, "nodeType": "796", "messageId": "797", "endLine": 5, "endColumn": 8}, {"ruleId": "794", "severity": 1, "message": "826", "line": 31, "column": 9, "nodeType": "796", "messageId": "797", "endLine": 31, "endColumn": 21}, {"ruleId": "794", "severity": 1, "message": "825", "line": 5, "column": 3, "nodeType": "796", "messageId": "797", "endLine": 5, "endColumn": 8}, {"ruleId": "794", "severity": 1, "message": "826", "line": 31, "column": 9, "nodeType": "796", "messageId": "797", "endLine": 31, "endColumn": 21}, {"ruleId": "794", "severity": 1, "message": "825", "line": 5, "column": 3, "nodeType": "796", "messageId": "797", "endLine": 5, "endColumn": 8}, {"ruleId": "794", "severity": 1, "message": "826", "line": 31, "column": 9, "nodeType": "796", "messageId": "797", "endLine": 31, "endColumn": 21}, {"ruleId": "794", "severity": 1, "message": "826", "line": 27, "column": 9, "nodeType": "796", "messageId": "797", "endLine": 27, "endColumn": 21}, {"ruleId": "794", "severity": 1, "message": "825", "line": 5, "column": 3, "nodeType": "796", "messageId": "797", "endLine": 5, "endColumn": 8}, {"ruleId": "794", "severity": 1, "message": "853", "line": 6, "column": 3, "nodeType": "796", "messageId": "797", "endLine": 6, "endColumn": 9}, {"ruleId": "794", "severity": 1, "message": "815", "line": 14, "column": 11, "nodeType": "796", "messageId": "797", "endLine": 14, "endColumn": 19}, {"ruleId": "794", "severity": 1, "message": "822", "line": 23, "column": 11, "nodeType": "796", "messageId": "797", "endLine": 23, "endColumn": 26}, {"ruleId": "794", "severity": 1, "message": "826", "line": 30, "column": 9, "nodeType": "796", "messageId": "797", "endLine": 30, "endColumn": 21}, {"ruleId": "794", "severity": 1, "message": "854", "line": 33, "column": 9, "nodeType": "796", "messageId": "797", "endLine": 33, "endColumn": 29}, {"ruleId": "794", "severity": 1, "message": "855", "line": 38, "column": 9, "nodeType": "796", "messageId": "797", "endLine": 38, "endColumn": 26}, {"ruleId": "794", "severity": 1, "message": "822", "line": 20, "column": 11, "nodeType": "796", "messageId": "797", "endLine": 20, "endColumn": 26}, {"ruleId": "794", "severity": 1, "message": "826", "line": 27, "column": 9, "nodeType": "796", "messageId": "797", "endLine": 27, "endColumn": 21}, {"ruleId": "794", "severity": 1, "message": "855", "line": 35, "column": 9, "nodeType": "796", "messageId": "797", "endLine": 35, "endColumn": 26}, {"ruleId": "794", "severity": 1, "message": "825", "line": 5, "column": 3, "nodeType": "796", "messageId": "797", "endLine": 5, "endColumn": 8}, {"ruleId": "794", "severity": 1, "message": "853", "line": 6, "column": 3, "nodeType": "796", "messageId": "797", "endLine": 6, "endColumn": 9}, {"ruleId": "794", "severity": 1, "message": "815", "line": 14, "column": 11, "nodeType": "796", "messageId": "797", "endLine": 14, "endColumn": 19}, {"ruleId": "794", "severity": 1, "message": "822", "line": 23, "column": 11, "nodeType": "796", "messageId": "797", "endLine": 23, "endColumn": 26}, {"ruleId": "794", "severity": 1, "message": "826", "line": 30, "column": 9, "nodeType": "796", "messageId": "797", "endLine": 30, "endColumn": 21}, {"ruleId": "794", "severity": 1, "message": "854", "line": 33, "column": 9, "nodeType": "796", "messageId": "797", "endLine": 33, "endColumn": 29}, {"ruleId": "794", "severity": 1, "message": "855", "line": 38, "column": 9, "nodeType": "796", "messageId": "797", "endLine": 38, "endColumn": 26}, {"ruleId": "794", "severity": 1, "message": "822", "line": 24, "column": 11, "nodeType": "796", "messageId": "797", "endLine": 24, "endColumn": 26}, {"ruleId": "837", "severity": 1, "message": "856", "line": 53, "column": 6, "nodeType": "839", "endLine": 53, "endColumn": 18, "suggestions": "857"}, {"ruleId": "794", "severity": 1, "message": "858", "line": 1, "column": 8, "nodeType": "796", "messageId": "797", "endLine": 1, "endColumn": 13}, {"ruleId": "794", "severity": 1, "message": "859", "line": 5, "column": 7, "nodeType": "796", "messageId": "797", "endLine": 5, "endColumn": 14}, {"ruleId": "794", "severity": 1, "message": "814", "line": 14, "column": 3, "nodeType": "796", "messageId": "797", "endLine": 14, "endColumn": 10}, {"ruleId": "794", "severity": 1, "message": "860", "line": 28, "column": 10, "nodeType": "796", "messageId": "797", "endLine": 28, "endColumn": 18}, {"ruleId": "794", "severity": 1, "message": "858", "line": 1, "column": 8, "nodeType": "796", "messageId": "797", "endLine": 1, "endColumn": 13}, {"ruleId": "794", "severity": 1, "message": "859", "line": 5, "column": 7, "nodeType": "796", "messageId": "797", "endLine": 5, "endColumn": 14}, {"ruleId": "794", "severity": 1, "message": "809", "line": 8, "column": 3, "nodeType": "796", "messageId": "797", "endLine": 8, "endColumn": 7}, {"ruleId": "794", "severity": 1, "message": "810", "line": 9, "column": 3, "nodeType": "796", "messageId": "797", "endLine": 9, "endColumn": 14}, {"ruleId": "794", "severity": 1, "message": "813", "line": 10, "column": 3, "nodeType": "796", "messageId": "797", "endLine": 10, "endColumn": 14}, {"ruleId": "794", "severity": 1, "message": "861", "line": 23, "column": 3, "nodeType": "796", "messageId": "797", "endLine": 23, "endColumn": 15}, {"ruleId": "794", "severity": 1, "message": "862", "line": 24, "column": 3, "nodeType": "796", "messageId": "797", "endLine": 24, "endColumn": 17}, {"ruleId": "794", "severity": 1, "message": "816", "line": 46, "column": 15, "nodeType": "796", "messageId": "797", "endLine": 46, "endColumn": 27}, {"ruleId": "794", "severity": 1, "message": "863", "line": 47, "column": 12, "nodeType": "796", "messageId": "797", "endLine": 47, "endColumn": 21}, {"ruleId": "837", "severity": 1, "message": "864", "line": 134, "column": 6, "nodeType": "839", "endLine": 134, "endColumn": 18, "suggestions": "865"}, {"ruleId": "794", "severity": 1, "message": "809", "line": 8, "column": 3, "nodeType": "796", "messageId": "797", "endLine": 8, "endColumn": 7}, {"ruleId": "794", "severity": 1, "message": "810", "line": 9, "column": 3, "nodeType": "796", "messageId": "797", "endLine": 9, "endColumn": 14}, {"ruleId": "794", "severity": 1, "message": "813", "line": 10, "column": 3, "nodeType": "796", "messageId": "797", "endLine": 10, "endColumn": 14}, {"ruleId": "794", "severity": 1, "message": "861", "line": 23, "column": 3, "nodeType": "796", "messageId": "797", "endLine": 23, "endColumn": 15}, {"ruleId": "794", "severity": 1, "message": "862", "line": 24, "column": 3, "nodeType": "796", "messageId": "797", "endLine": 24, "endColumn": 17}, {"ruleId": "794", "severity": 1, "message": "814", "line": 25, "column": 3, "nodeType": "796", "messageId": "797", "endLine": 25, "endColumn": 10}, {"ruleId": "794", "severity": 1, "message": "817", "line": 37, "column": 10, "nodeType": "796", "messageId": "797", "endLine": 37, "endColumn": 17}, {"ruleId": "794", "severity": 1, "message": "866", "line": 41, "column": 13, "nodeType": "796", "messageId": "797", "endLine": 41, "endColumn": 23}, {"ruleId": "794", "severity": 1, "message": "816", "line": 43, "column": 15, "nodeType": "796", "messageId": "797", "endLine": 43, "endColumn": 27}, {"ruleId": "794", "severity": 1, "message": "867", "line": 44, "column": 17, "nodeType": "796", "messageId": "797", "endLine": 44, "endColumn": 31}, {"ruleId": "837", "severity": 1, "message": "868", "line": 98, "column": 6, "nodeType": "839", "endLine": 98, "endColumn": 18, "suggestions": "869"}, {"ruleId": "794", "severity": 1, "message": "870", "line": 101, "column": 9, "nodeType": "796", "messageId": "797", "endLine": 101, "endColumn": 27}, {"ruleId": "794", "severity": 1, "message": "809", "line": 8, "column": 3, "nodeType": "796", "messageId": "797", "endLine": 8, "endColumn": 7}, {"ruleId": "794", "severity": 1, "message": "810", "line": 9, "column": 3, "nodeType": "796", "messageId": "797", "endLine": 9, "endColumn": 14}, {"ruleId": "794", "severity": 1, "message": "813", "line": 10, "column": 3, "nodeType": "796", "messageId": "797", "endLine": 10, "endColumn": 14}, {"ruleId": "794", "severity": 1, "message": "861", "line": 23, "column": 3, "nodeType": "796", "messageId": "797", "endLine": 23, "endColumn": 15}, {"ruleId": "794", "severity": 1, "message": "862", "line": 24, "column": 3, "nodeType": "796", "messageId": "797", "endLine": 24, "endColumn": 17}, {"ruleId": "794", "severity": 1, "message": "814", "line": 25, "column": 3, "nodeType": "796", "messageId": "797", "endLine": 25, "endColumn": 10}, {"ruleId": "794", "severity": 1, "message": "828", "line": 29, "column": 3, "nodeType": "796", "messageId": "797", "endLine": 29, "endColumn": 13}, {"ruleId": "794", "severity": 1, "message": "818", "line": 39, "column": 11, "nodeType": "796", "messageId": "797", "endLine": 39, "endColumn": 19}, {"ruleId": "794", "severity": 1, "message": "816", "line": 43, "column": 15, "nodeType": "796", "messageId": "797", "endLine": 43, "endColumn": 27}, {"ruleId": "794", "severity": 1, "message": "871", "line": 44, "column": 14, "nodeType": "796", "messageId": "797", "endLine": 44, "endColumn": 25}, {"ruleId": "794", "severity": 1, "message": "872", "line": 50, "column": 69, "nodeType": "796", "messageId": "797", "endLine": 50, "endColumn": 76}, {"ruleId": "794", "severity": 1, "message": "873", "line": 79, "column": 10, "nodeType": "796", "messageId": "797", "endLine": 79, "endColumn": 26}, {"ruleId": "837", "severity": 1, "message": "874", "line": 145, "column": 6, "nodeType": "839", "endLine": 145, "endColumn": 8, "suggestions": "875"}, {"ruleId": "794", "severity": 1, "message": "876", "line": 689, "column": 9, "nodeType": "796", "messageId": "797", "endLine": 689, "endColumn": 26}, {"ruleId": "794", "severity": 1, "message": "877", "line": 20, "column": 3, "nodeType": "796", "messageId": "797", "endLine": 20, "endColumn": 13}, {"ruleId": "794", "severity": 1, "message": "878", "line": 21, "column": 3, "nodeType": "796", "messageId": "797", "endLine": 21, "endColumn": 9}, {"ruleId": "794", "severity": 1, "message": "879", "line": 22, "column": 3, "nodeType": "796", "messageId": "797", "endLine": 22, "endColumn": 11}, {"ruleId": "794", "severity": 1, "message": "808", "line": 23, "column": 3, "nodeType": "796", "messageId": "797", "endLine": 23, "endColumn": 7}, {"ruleId": "794", "severity": 1, "message": "880", "line": 26, "column": 3, "nodeType": "796", "messageId": "797", "endLine": 26, "endColumn": 17}, {"ruleId": "794", "severity": 1, "message": "881", "line": 69, "column": 10, "nodeType": "796", "messageId": "797", "endLine": 69, "endColumn": 22}, {"ruleId": "882", "severity": 1, "message": "883", "line": 466, "column": 9, "nodeType": "884", "messageId": "885", "endLine": 469, "endColumn": 10}, {"ruleId": "837", "severity": 1, "message": "886", "line": 95, "column": 6, "nodeType": "839", "endLine": 95, "endColumn": 21, "suggestions": "887", "suppressions": "888"}, {"ruleId": "790", "severity": 1, "message": "791", "line": 260, "column": 9, "nodeType": "792", "messageId": "793", "endLine": 264, "endColumn": 11}, {"ruleId": "790", "severity": 1, "message": "791", "line": 274, "column": 9, "nodeType": "792", "messageId": "793", "endLine": 274, "endColumn": 70}, {"ruleId": "790", "severity": 1, "message": "791", "line": 278, "column": 9, "nodeType": "792", "messageId": "793", "endLine": 278, "endColumn": 54}, {"ruleId": "790", "severity": 1, "message": "791", "line": 333, "column": 11, "nodeType": "792", "messageId": "793", "endLine": 338, "endColumn": 13}, {"ruleId": "790", "severity": 1, "message": "791", "line": 435, "column": 9, "nodeType": "792", "messageId": "793", "endLine": 439, "endColumn": 11}, {"ruleId": "790", "severity": 1, "message": "791", "line": 451, "column": 9, "nodeType": "792", "messageId": "793", "endLine": 451, "endColumn": 54}, {"ruleId": "790", "severity": 1, "message": "791", "line": 668, "column": 9, "nodeType": "792", "messageId": "793", "endLine": 668, "endColumn": 163}, {"ruleId": "790", "severity": 1, "message": "791", "line": 677, "column": 9, "nodeType": "792", "messageId": "793", "endLine": 677, "endColumn": 70}, {"ruleId": "790", "severity": 1, "message": "791", "line": 681, "column": 9, "nodeType": "792", "messageId": "793", "endLine": 681, "endColumn": 54}, {"ruleId": "794", "severity": 1, "message": "889", "line": 755, "column": 17, "nodeType": "796", "messageId": "797", "endLine": 755, "endColumn": 22}, {"ruleId": "790", "severity": 1, "message": "791", "line": 775, "column": 9, "nodeType": "792", "messageId": "793", "endLine": 779, "endColumn": 11}, {"ruleId": "790", "severity": 1, "message": "791", "line": 794, "column": 11, "nodeType": "792", "messageId": "793", "endLine": 798, "endColumn": 13}, {"ruleId": "790", "severity": 1, "message": "791", "line": 801, "column": 9, "nodeType": "792", "messageId": "793", "endLine": 804, "endColumn": 11}, {"ruleId": "790", "severity": 1, "message": "791", "line": 810, "column": 11, "nodeType": "792", "messageId": "793", "endLine": 814, "endColumn": 13}, {"ruleId": "790", "severity": 1, "message": "791", "line": 817, "column": 9, "nodeType": "792", "messageId": "793", "endLine": 820, "endColumn": 11}, {"ruleId": "790", "severity": 1, "message": "791", "line": 885, "column": 9, "nodeType": "792", "messageId": "793", "endLine": 889, "endColumn": 11}, {"ruleId": "890", "severity": 1, "message": "891", "line": 955, "column": 3, "nodeType": "892", "messageId": "893", "endLine": 955, "endColumn": 29}, {"ruleId": "890", "severity": 1, "message": "894", "line": 1143, "column": 3, "nodeType": "892", "messageId": "893", "endLine": 1143, "endColumn": 23}, {"ruleId": "890", "severity": 1, "message": "895", "line": 1238, "column": 3, "nodeType": "892", "messageId": "893", "endLine": 1238, "endColumn": 20}, {"ruleId": "790", "severity": 1, "message": "791", "line": 1287, "column": 9, "nodeType": "792", "messageId": "793", "endLine": 1287, "endColumn": 163}, {"ruleId": "790", "severity": 1, "message": "791", "line": 1317, "column": 9, "nodeType": "792", "messageId": "793", "endLine": 1317, "endColumn": 163}, {"ruleId": "790", "severity": 1, "message": "791", "line": 1370, "column": 9, "nodeType": "792", "messageId": "793", "endLine": 1370, "endColumn": 163}, {"ruleId": "790", "severity": 1, "message": "791", "line": 1412, "column": 9, "nodeType": "792", "messageId": "793", "endLine": 1412, "endColumn": 163}, {"ruleId": "794", "severity": 1, "message": "896", "line": 6, "column": 3, "nodeType": "796", "messageId": "797", "endLine": 6, "endColumn": 8}, {"ruleId": "794", "severity": 1, "message": "814", "line": 11, "column": 3, "nodeType": "796", "messageId": "797", "endLine": 11, "endColumn": 10}, {"ruleId": "794", "severity": 1, "message": "858", "line": 1, "column": 8, "nodeType": "796", "messageId": "797", "endLine": 1, "endColumn": 13}, {"ruleId": "794", "severity": 1, "message": "859", "line": 5, "column": 7, "nodeType": "796", "messageId": "797", "endLine": 5, "endColumn": 14}, {"ruleId": "794", "severity": 1, "message": "897", "line": 3, "column": 3, "nodeType": "796", "messageId": "797", "endLine": 3, "endColumn": 11}, {"ruleId": "794", "severity": 1, "message": "898", "line": 4, "column": 3, "nodeType": "796", "messageId": "797", "endLine": 4, "endColumn": 6}, {"ruleId": "794", "severity": 1, "message": "899", "line": 5, "column": 3, "nodeType": "796", "messageId": "797", "endLine": 5, "endColumn": 7}, {"ruleId": "794", "severity": 1, "message": "900", "line": 6, "column": 3, "nodeType": "796", "messageId": "797", "endLine": 6, "endColumn": 11}, {"ruleId": "794", "severity": 1, "message": "901", "line": 7, "column": 3, "nodeType": "796", "messageId": "797", "endLine": 7, "endColumn": 6}, {"ruleId": "794", "severity": 1, "message": "902", "line": 12, "column": 3, "nodeType": "796", "messageId": "797", "endLine": 12, "endColumn": 9}, {"ruleId": "794", "severity": 1, "message": "903", "line": 36, "column": 9, "nodeType": "796", "messageId": "797", "endLine": 36, "endColumn": 21}, {"ruleId": "794", "severity": 1, "message": "904", "line": 50, "column": 9, "nodeType": "796", "messageId": "797", "endLine": 50, "endColumn": 17}, {"ruleId": "794", "severity": 1, "message": "905", "line": 64, "column": 9, "nodeType": "796", "messageId": "797", "endLine": 64, "endColumn": 20}, {"ruleId": "794", "severity": 1, "message": "906", "line": 88, "column": 9, "nodeType": "796", "messageId": "797", "endLine": 88, "endColumn": 22}, {"ruleId": "794", "severity": 1, "message": "907", "line": 104, "column": 9, "nodeType": "796", "messageId": "797", "endLine": 104, "endColumn": 30}, {"ruleId": "794", "severity": 1, "message": "908", "line": 3, "column": 3, "nodeType": "796", "messageId": "797", "endLine": 3, "endColumn": 12}, {"ruleId": "794", "severity": 1, "message": "900", "line": 3, "column": 3, "nodeType": "796", "messageId": "797", "endLine": 3, "endColumn": 11}, {"ruleId": "794", "severity": 1, "message": "901", "line": 4, "column": 3, "nodeType": "796", "messageId": "797", "endLine": 4, "endColumn": 6}, {"ruleId": "794", "severity": 1, "message": "909", "line": 5, "column": 3, "nodeType": "796", "messageId": "797", "endLine": 5, "endColumn": 8}, {"ruleId": "794", "severity": 1, "message": "910", "line": 6, "column": 3, "nodeType": "796", "messageId": "797", "endLine": 6, "endColumn": 8}, {"ruleId": "794", "severity": 1, "message": "911", "line": 7, "column": 3, "nodeType": "796", "messageId": "797", "endLine": 7, "endColumn": 16}, {"ruleId": "794", "severity": 1, "message": "844", "line": 8, "column": 3, "nodeType": "796", "messageId": "797", "endLine": 8, "endColumn": 10}, {"ruleId": "794", "severity": 1, "message": "902", "line": 9, "column": 3, "nodeType": "796", "messageId": "797", "endLine": 9, "endColumn": 9}, {"ruleId": "794", "severity": 1, "message": "912", "line": 10, "column": 3, "nodeType": "796", "messageId": "797", "endLine": 10, "endColumn": 22}, {"ruleId": "794", "severity": 1, "message": "897", "line": 11, "column": 3, "nodeType": "796", "messageId": "797", "endLine": 11, "endColumn": 11}, {"ruleId": "794", "severity": 1, "message": "898", "line": 12, "column": 3, "nodeType": "796", "messageId": "797", "endLine": 12, "endColumn": 6}, {"ruleId": "794", "severity": 1, "message": "899", "line": 13, "column": 3, "nodeType": "796", "messageId": "797", "endLine": 13, "endColumn": 7}, {"ruleId": "794", "severity": 1, "message": "913", "line": 14, "column": 3, "nodeType": "796", "messageId": "797", "endLine": 14, "endColumn": 16}, {"ruleId": "794", "severity": 1, "message": "914", "line": 15, "column": 3, "nodeType": "796", "messageId": "797", "endLine": 15, "endColumn": 7}, {"ruleId": "794", "severity": 1, "message": "908", "line": 16, "column": 3, "nodeType": "796", "messageId": "797", "endLine": 16, "endColumn": 12}, {"ruleId": "794", "severity": 1, "message": "915", "line": 18, "column": 40, "nodeType": "796", "messageId": "797", "endLine": 18, "endColumn": 44}, {"ruleId": "794", "severity": 1, "message": "916", "line": 47, "column": 9, "nodeType": "796", "messageId": "797", "endLine": 47, "endColumn": 19}, {"ruleId": "794", "severity": 1, "message": "917", "line": 64, "column": 9, "nodeType": "796", "messageId": "797", "endLine": 64, "endColumn": 19}, {"ruleId": "794", "severity": 1, "message": "918", "line": 71, "column": 9, "nodeType": "796", "messageId": "797", "endLine": 71, "endColumn": 20}, {"ruleId": "794", "severity": 1, "message": "906", "line": 79, "column": 9, "nodeType": "796", "messageId": "797", "endLine": 79, "endColumn": 22}, {"ruleId": "794", "severity": 1, "message": "907", "line": 95, "column": 9, "nodeType": "796", "messageId": "797", "endLine": 95, "endColumn": 30}, {"ruleId": "794", "severity": 1, "message": "919", "line": 290, "column": 27, "nodeType": "796", "messageId": "797", "endLine": 290, "endColumn": 37}, {"ruleId": "794", "severity": 1, "message": "920", "line": 291, "column": 27, "nodeType": "796", "messageId": "797", "endLine": 291, "endColumn": 36}, {"ruleId": "794", "severity": 1, "message": "921", "line": 14, "column": 3, "nodeType": "796", "messageId": "797", "endLine": 14, "endColumn": 15}, {"ruleId": "794", "severity": 1, "message": "922", "line": 15, "column": 3, "nodeType": "796", "messageId": "797", "endLine": 15, "endColumn": 10}, {"ruleId": "794", "severity": 1, "message": "923", "line": 82, "column": 9, "nodeType": "796", "messageId": "797", "endLine": 82, "endColumn": 23}, {"ruleId": "794", "severity": 1, "message": "924", "line": 106, "column": 9, "nodeType": "796", "messageId": "797", "endLine": 106, "endColumn": 23}, {"ruleId": "794", "severity": 1, "message": "913", "line": 14, "column": 3, "nodeType": "796", "messageId": "797", "endLine": 14, "endColumn": 16}, {"ruleId": "794", "severity": 1, "message": "914", "line": 15, "column": 3, "nodeType": "796", "messageId": "797", "endLine": 15, "endColumn": 7}, {"ruleId": "794", "severity": 1, "message": "825", "line": 3, "column": 3, "nodeType": "796", "messageId": "797", "endLine": 3, "endColumn": 8}, {"ruleId": "837", "severity": 1, "message": "925", "line": 54, "column": 6, "nodeType": "839", "endLine": 54, "endColumn": 34, "suggestions": "926"}, {"ruleId": "794", "severity": 1, "message": "927", "line": 25, "column": 13, "nodeType": "796", "messageId": "797", "endLine": 25, "endColumn": 25}, {"ruleId": "794", "severity": 1, "message": "928", "line": 33, "column": 3, "nodeType": "796", "messageId": "797", "endLine": 33, "endColumn": 15}, {"ruleId": "794", "severity": 1, "message": "929", "line": 34, "column": 3, "nodeType": "796", "messageId": "797", "endLine": 34, "endColumn": 14}, {"ruleId": "794", "severity": 1, "message": "930", "line": 35, "column": 3, "nodeType": "796", "messageId": "797", "endLine": 35, "endColumn": 22}, {"ruleId": "794", "severity": 1, "message": "931", "line": 36, "column": 3, "nodeType": "796", "messageId": "797", "endLine": 36, "endColumn": 21}, {"ruleId": "794", "severity": 1, "message": "932", "line": 37, "column": 3, "nodeType": "796", "messageId": "797", "endLine": 37, "endColumn": 17}, {"ruleId": "794", "severity": 1, "message": "933", "line": 41, "column": 3, "nodeType": "796", "messageId": "797", "endLine": 41, "endColumn": 20}, {"ruleId": "794", "severity": 1, "message": "934", "line": 43, "column": 10, "nodeType": "796", "messageId": "797", "endLine": 43, "endColumn": 34}, {"ruleId": "794", "severity": 1, "message": "935", "line": 69, "column": 10, "nodeType": "796", "messageId": "797", "endLine": 69, "endColumn": 17}, {"ruleId": "794", "severity": 1, "message": "936", "line": 69, "column": 19, "nodeType": "796", "messageId": "797", "endLine": 69, "endColumn": 29}, {"ruleId": "837", "severity": 1, "message": "937", "line": 88, "column": 6, "nodeType": "839", "endLine": 88, "endColumn": 18, "suggestions": "938"}, {"ruleId": "837", "severity": 1, "message": "939", "line": 448, "column": 6, "nodeType": "839", "endLine": 448, "endColumn": 28, "suggestions": "940"}, {"ruleId": "794", "severity": 1, "message": "941", "line": 4, "column": 3, "nodeType": "796", "messageId": "797", "endLine": 4, "endColumn": 12}, {"ruleId": "794", "severity": 1, "message": "942", "line": 8, "column": 3, "nodeType": "796", "messageId": "797", "endLine": 8, "endColumn": 7}, {"ruleId": "794", "severity": 1, "message": "943", "line": 9, "column": 3, "nodeType": "796", "messageId": "797", "endLine": 9, "endColumn": 11}, {"ruleId": "794", "severity": 1, "message": "944", "line": 10, "column": 3, "nodeType": "796", "messageId": "797", "endLine": 10, "endColumn": 15}, {"ruleId": "837", "severity": 1, "message": "925", "line": 46, "column": 6, "nodeType": "839", "endLine": 46, "endColumn": 18, "suggestions": "945"}, {"ruleId": "794", "severity": 1, "message": "946", "line": 9, "column": 3, "nodeType": "796", "messageId": "797", "endLine": 9, "endColumn": 14}, {"ruleId": "794", "severity": 1, "message": "877", "line": 10, "column": 3, "nodeType": "796", "messageId": "797", "endLine": 10, "endColumn": 13}, {"ruleId": "794", "severity": 1, "message": "878", "line": 11, "column": 3, "nodeType": "796", "messageId": "797", "endLine": 11, "endColumn": 9}, {"ruleId": "794", "severity": 1, "message": "879", "line": 12, "column": 3, "nodeType": "796", "messageId": "797", "endLine": 12, "endColumn": 11}, {"ruleId": "794", "severity": 1, "message": "944", "line": 33, "column": 3, "nodeType": "796", "messageId": "797", "endLine": 33, "endColumn": 15}, {"ruleId": "794", "severity": 1, "message": "947", "line": 35, "column": 3, "nodeType": "796", "messageId": "797", "endLine": 35, "endColumn": 26}, {"ruleId": "794", "severity": 1, "message": "871", "line": 42, "column": 14, "nodeType": "796", "messageId": "797", "endLine": 42, "endColumn": 25}, {"ruleId": "794", "severity": 1, "message": "928", "line": 52, "column": 3, "nodeType": "796", "messageId": "797", "endLine": 52, "endColumn": 15}, {"ruleId": "794", "severity": 1, "message": "929", "line": 53, "column": 3, "nodeType": "796", "messageId": "797", "endLine": 53, "endColumn": 14}, {"ruleId": "794", "severity": 1, "message": "930", "line": 54, "column": 3, "nodeType": "796", "messageId": "797", "endLine": 54, "endColumn": 22}, {"ruleId": "794", "severity": 1, "message": "931", "line": 55, "column": 3, "nodeType": "796", "messageId": "797", "endLine": 55, "endColumn": 21}, {"ruleId": "794", "severity": 1, "message": "932", "line": 56, "column": 3, "nodeType": "796", "messageId": "797", "endLine": 56, "endColumn": 17}, {"ruleId": "794", "severity": 1, "message": "948", "line": 57, "column": 3, "nodeType": "796", "messageId": "797", "endLine": 57, "endColumn": 15}, {"ruleId": "794", "severity": 1, "message": "949", "line": 58, "column": 3, "nodeType": "796", "messageId": "797", "endLine": 58, "endColumn": 19}, {"ruleId": "794", "severity": 1, "message": "950", "line": 59, "column": 3, "nodeType": "796", "messageId": "797", "endLine": 59, "endColumn": 21}, {"ruleId": "794", "severity": 1, "message": "832", "line": 72, "column": 9, "nodeType": "796", "messageId": "797", "endLine": 72, "endColumn": 17}, {"ruleId": "794", "severity": 1, "message": "951", "line": 79, "column": 10, "nodeType": "796", "messageId": "797", "endLine": 79, "endColumn": 23}, {"ruleId": "794", "severity": 1, "message": "952", "line": 79, "column": 25, "nodeType": "796", "messageId": "797", "endLine": 79, "endColumn": 41}, {"ruleId": "794", "severity": 1, "message": "953", "line": 80, "column": 10, "nodeType": "796", "messageId": "797", "endLine": 80, "endColumn": 27}, {"ruleId": "794", "severity": 1, "message": "954", "line": 85, "column": 10, "nodeType": "796", "messageId": "797", "endLine": 85, "endColumn": 26}, {"ruleId": "837", "severity": 1, "message": "925", "line": 105, "column": 6, "nodeType": "839", "endLine": 105, "endColumn": 18, "suggestions": "955"}, {"ruleId": "837", "severity": 1, "message": "956", "line": 112, "column": 6, "nodeType": "839", "endLine": 112, "endColumn": 20, "suggestions": "957"}, {"ruleId": "837", "severity": 1, "message": "958", "line": 127, "column": 6, "nodeType": "839", "endLine": 127, "endColumn": 34, "suggestions": "959"}, {"ruleId": "794", "severity": 1, "message": "960", "line": 283, "column": 13, "nodeType": "796", "messageId": "797", "endLine": 283, "endColumn": 19}, {"ruleId": "794", "severity": 1, "message": "880", "line": 17, "column": 3, "nodeType": "796", "messageId": "797", "endLine": 17, "endColumn": 17}, {"ruleId": "794", "severity": 1, "message": "944", "line": 34, "column": 3, "nodeType": "796", "messageId": "797", "endLine": 34, "endColumn": 15}, {"ruleId": "794", "severity": 1, "message": "947", "line": 35, "column": 3, "nodeType": "796", "messageId": "797", "endLine": 35, "endColumn": 26}, {"ruleId": "794", "severity": 1, "message": "961", "line": 39, "column": 11, "nodeType": "796", "messageId": "797", "endLine": 39, "endColumn": 19}, {"ruleId": "794", "severity": 1, "message": "928", "line": 51, "column": 3, "nodeType": "796", "messageId": "797", "endLine": 51, "endColumn": 15}, {"ruleId": "794", "severity": 1, "message": "929", "line": 52, "column": 3, "nodeType": "796", "messageId": "797", "endLine": 52, "endColumn": 14}, {"ruleId": "794", "severity": 1, "message": "931", "line": 54, "column": 3, "nodeType": "796", "messageId": "797", "endLine": 54, "endColumn": 21}, {"ruleId": "794", "severity": 1, "message": "932", "line": 55, "column": 3, "nodeType": "796", "messageId": "797", "endLine": 55, "endColumn": 17}, {"ruleId": "794", "severity": 1, "message": "934", "line": 62, "column": 10, "nodeType": "796", "messageId": "797", "endLine": 62, "endColumn": 34}, {"ruleId": "794", "severity": 1, "message": "962", "line": 105, "column": 10, "nodeType": "796", "messageId": "797", "endLine": 105, "endColumn": 26}, {"ruleId": "794", "severity": 1, "message": "963", "line": 105, "column": 28, "nodeType": "796", "messageId": "797", "endLine": 105, "endColumn": 47}, {"ruleId": "837", "severity": 1, "message": "956", "line": 145, "column": 6, "nodeType": "839", "endLine": 145, "endColumn": 18, "suggestions": "964"}, {"ruleId": "794", "severity": 1, "message": "965", "line": 701, "column": 9, "nodeType": "796", "messageId": "797", "endLine": 701, "endColumn": 19}, {"ruleId": "794", "severity": 1, "message": "966", "line": 1311, "column": 11, "nodeType": "796", "messageId": "797", "endLine": 1311, "endColumn": 28}, {"ruleId": "794", "severity": 1, "message": "967", "line": 1316, "column": 11, "nodeType": "796", "messageId": "797", "endLine": 1316, "endColumn": 30}, {"ruleId": "794", "severity": 1, "message": "968", "line": 1883, "column": 9, "nodeType": "796", "messageId": "797", "endLine": 1883, "endColumn": 23}, {"ruleId": "794", "severity": 1, "message": "858", "line": 1, "column": 8, "nodeType": "796", "messageId": "797", "endLine": 1, "endColumn": 13}, {"ruleId": "794", "severity": 1, "message": "859", "line": 5, "column": 7, "nodeType": "796", "messageId": "797", "endLine": 5, "endColumn": 14}, {"ruleId": "794", "severity": 1, "message": "858", "line": 1, "column": 8, "nodeType": "796", "messageId": "797", "endLine": 1, "endColumn": 13}, {"ruleId": "794", "severity": 1, "message": "859", "line": 5, "column": 7, "nodeType": "796", "messageId": "797", "endLine": 5, "endColumn": 14}, {"ruleId": "794", "severity": 1, "message": "858", "line": 1, "column": 8, "nodeType": "796", "messageId": "797", "endLine": 1, "endColumn": 13}, {"ruleId": "794", "severity": 1, "message": "859", "line": 5, "column": 7, "nodeType": "796", "messageId": "797", "endLine": 5, "endColumn": 14}, {"ruleId": "794", "severity": 1, "message": "858", "line": 1, "column": 8, "nodeType": "796", "messageId": "797", "endLine": 1, "endColumn": 13}, {"ruleId": "794", "severity": 1, "message": "859", "line": 5, "column": 7, "nodeType": "796", "messageId": "797", "endLine": 5, "endColumn": 14}, {"ruleId": "794", "severity": 1, "message": "969", "line": 83, "column": 13, "nodeType": "796", "messageId": "797", "endLine": 83, "endColumn": 21}, {"ruleId": "790", "severity": 1, "message": "791", "line": 109, "column": 9, "nodeType": "792", "messageId": "793", "endLine": 109, "endColumn": 163}, {"ruleId": "790", "severity": 1, "message": "791", "line": 123, "column": 9, "nodeType": "792", "messageId": "793", "endLine": 123, "endColumn": 70}, {"ruleId": "790", "severity": 1, "message": "791", "line": 127, "column": 9, "nodeType": "792", "messageId": "793", "endLine": 127, "endColumn": 54}, {"ruleId": "790", "severity": 1, "message": "791", "line": 212, "column": 9, "nodeType": "792", "messageId": "793", "endLine": 212, "endColumn": 163}, {"ruleId": "790", "severity": 1, "message": "791", "line": 226, "column": 9, "nodeType": "792", "messageId": "793", "endLine": 226, "endColumn": 70}, {"ruleId": "790", "severity": 1, "message": "791", "line": 230, "column": 9, "nodeType": "792", "messageId": "793", "endLine": 230, "endColumn": 54}, {"ruleId": "790", "severity": 1, "message": "791", "line": 271, "column": 9, "nodeType": "792", "messageId": "793", "endLine": 271, "endColumn": 163}, {"ruleId": "790", "severity": 1, "message": "791", "line": 280, "column": 9, "nodeType": "792", "messageId": "793", "endLine": 280, "endColumn": 70}, {"ruleId": "790", "severity": 1, "message": "791", "line": 284, "column": 9, "nodeType": "792", "messageId": "793", "endLine": 284, "endColumn": 54}, {"ruleId": "790", "severity": 1, "message": "791", "line": 320, "column": 9, "nodeType": "792", "messageId": "793", "endLine": 320, "endColumn": 70}, {"ruleId": "790", "severity": 1, "message": "791", "line": 324, "column": 9, "nodeType": "792", "messageId": "793", "endLine": 324, "endColumn": 54}, {"ruleId": "790", "severity": 1, "message": "791", "line": 360, "column": 9, "nodeType": "792", "messageId": "793", "endLine": 360, "endColumn": 163}, {"ruleId": "790", "severity": 1, "message": "791", "line": 369, "column": 9, "nodeType": "792", "messageId": "793", "endLine": 369, "endColumn": 70}, {"ruleId": "790", "severity": 1, "message": "791", "line": 373, "column": 9, "nodeType": "792", "messageId": "793", "endLine": 373, "endColumn": 54}, {"ruleId": "790", "severity": 1, "message": "791", "line": 450, "column": 9, "nodeType": "792", "messageId": "793", "endLine": 450, "endColumn": 163}, {"ruleId": "790", "severity": 1, "message": "791", "line": 459, "column": 9, "nodeType": "792", "messageId": "793", "endLine": 459, "endColumn": 70}, {"ruleId": "790", "severity": 1, "message": "791", "line": 463, "column": 9, "nodeType": "792", "messageId": "793", "endLine": 463, "endColumn": 54}, {"ruleId": "794", "severity": 1, "message": "970", "line": 12, "column": 3, "nodeType": "796", "messageId": "797", "endLine": 12, "endColumn": 19}, {"ruleId": "794", "severity": 1, "message": "817", "line": 27, "column": 10, "nodeType": "796", "messageId": "797", "endLine": 27, "endColumn": 17}, {"ruleId": "794", "severity": 1, "message": "830", "line": 30, "column": 11, "nodeType": "796", "messageId": "797", "endLine": 30, "endColumn": 19}, {"ruleId": "794", "severity": 1, "message": "930", "line": 34, "column": 10, "nodeType": "796", "messageId": "797", "endLine": 34, "endColumn": 29}, {"ruleId": "794", "severity": 1, "message": "935", "line": 49, "column": 10, "nodeType": "796", "messageId": "797", "endLine": 49, "endColumn": 17}, {"ruleId": "794", "severity": 1, "message": "936", "line": 49, "column": 19, "nodeType": "796", "messageId": "797", "endLine": 49, "endColumn": 29}, {"ruleId": "837", "severity": 1, "message": "925", "line": 64, "column": 6, "nodeType": "839", "endLine": 64, "endColumn": 32, "suggestions": "971"}, {"ruleId": "794", "severity": 1, "message": "972", "line": 270, "column": 17, "nodeType": "796", "messageId": "797", "endLine": 270, "endColumn": 23}, {"ruleId": "794", "severity": 1, "message": "973", "line": 17, "column": 3, "nodeType": "796", "messageId": "797", "endLine": 17, "endColumn": 8}, {"ruleId": "794", "severity": 1, "message": "879", "line": 16, "column": 3, "nodeType": "796", "messageId": "797", "endLine": 16, "endColumn": 11}, {"ruleId": "794", "severity": 1, "message": "878", "line": 17, "column": 3, "nodeType": "796", "messageId": "797", "endLine": 17, "endColumn": 9}, {"ruleId": "794", "severity": 1, "message": "877", "line": 19, "column": 3, "nodeType": "796", "messageId": "797", "endLine": 19, "endColumn": 13}, {"ruleId": "794", "severity": 1, "message": "830", "line": 14, "column": 11, "nodeType": "796", "messageId": "797", "endLine": 14, "endColumn": 19}, {"ruleId": "794", "severity": 1, "message": "828", "line": 12, "column": 3, "nodeType": "796", "messageId": "797", "endLine": 12, "endColumn": 13}, {"ruleId": "794", "severity": 1, "message": "974", "line": 3, "column": 3, "nodeType": "796", "messageId": "797", "endLine": 3, "endColumn": 6}, {"ruleId": "794", "severity": 1, "message": "814", "line": 9, "column": 3, "nodeType": "796", "messageId": "797", "endLine": 9, "endColumn": 10}, "no-throw-literal", "Expected an error object to be thrown.", "ThrowStatement", "object", "no-unused-vars", "'Avatar' is defined but never used.", "Identifier", "unusedVar", "'AdminIcon' is defined but never used.", "'ConstructionIcon' is defined but never used.", "'CableIcon' is defined but never used.", "'ReportIcon' is defined but never used.", "'homeAnchorEl' is assigned a value but never used.", "'adminAnchorEl' is assigned a value but never used.", "'cantieriAnchorEl' is assigned a value but never used.", "'caviAnchorEl' is assigned a value but never used.", "'selectedCantiereName' is assigned a value but never used.", "'React' is defined but never used.", "'Grid' is defined but never used.", "'Card' is defined but never used.", "'CardContent' is defined but never used.", "'CardActionArea' is defined but never used.", "'navigateTo' is assigned a value but never used.", "'CardActions' is defined but never used.", "'Divider' is defined but never used.", "'HomeIcon' is defined but never used.", "'ViewListIcon' is defined but never used.", "'AddIcon' is defined but never used.", "'EditIcon' is defined but never used.", "'DeleteIcon' is defined but never used.", "'HistoryIcon' is defined but never used.", "'ParcoCavi' is defined but never used.", "'isImpersonating' is assigned a value but never used.", "'handleSuccess' is assigned a value but never used.", "'handleError' is assigned a value but never used.", "'Paper' is defined but never used.", "'cantiereName' is assigned a value but never used.", "'lastCheck' is assigned a value but never used.", "'IconButton' is defined but never used.", "'LinearProgress' is defined but never used.", "'InfoIcon' is defined but never used.", "'CavoForm' is defined but never used.", "'navigate' is assigned a value but never used.", "'setFilters' is assigned a value but never used.", "'statiInstallazione' is assigned a value but never used.", "'tipologieCavi' is assigned a value but never used.", "'setTipologieCavi' is assigned a value but never used.", "react-hooks/exhaustive-deps", "React Hook useEffect has missing dependencies: 'error' and 'user'. Either include them or remove the dependency array.", "ArrayExpression", ["975"], "'handleOpenDetails' is assigned a value but never used.", "'useEffect' is defined but never used.", "'useAuth' is assigned a value but never used.", "'Tooltip' is defined but never used.", "'PieChartIcon' is defined but never used.", "'ArrowBackIcon' is defined but never used.", "'ReportSection' is defined but never used.", "'CaviStatoChart' is defined but never used.", "'user' is assigned a value but never used.", "'reportTypes' is assigned a value but never used.", "'handleReportSelect' is assigned a value but never used.", "'renderReportContent' is assigned a value but never used.", "'Button' is defined but never used.", "'handleBackToCantieri' is assigned a value but never used.", "'handleBackToAdmin' is assigned a value but never used.", "React Hook useEffect has a missing dependency: 'selectCantiere'. Either include it or remove the dependency array.", ["976"], "'axios' is defined but never used.", "'API_URL' is assigned a value but never used.", "'filePath' is assigned a value but never used.", "'ListItemIcon' is defined but never used.", "'ListItemButton' is defined but never used.", "'BuildIcon' is defined but never used.", "React Hook useEffect has a missing dependency: 'loadCertificazioni'. Either include it or remove the dependency array.", ["977"], "'SearchIcon' is defined but never used.", "'AssignmentIcon' is defined but never used.", "React Hook useEffect has a missing dependency: 'loadComande'. Either include it or remove the dependency array.", ["978"], "'handleOptionSelect' is assigned a value but never used.", "'WarningIcon' is defined but never used.", "'isEmpty' is defined but never used.", "'isFirstInsertion' is assigned a value but never used.", "React Hook useEffect has missing dependencies: 'handleOptionSelect', 'initialOption', and 'loadBobine'. Either include them or remove the dependency array.", ["979"], "'renderBobineCards' is assigned a value but never used.", "'InputLabel' is defined but never used.", "'Select' is defined but never used.", "'MenuItem' is defined but never used.", "'FormHelperText' is defined but never used.", "'formWarnings' is assigned a value but never used.", "no-unreachable", "Unreachable code.", "IfStatement", "unreachableCode", "React Hook React.useEffect has a missing dependency: 'loadCavi'. Either include it or remove the dependency array.", ["980"], ["981"], "'token' is assigned a value but never used.", "no-dupe-keys", "Duplicate key 'updateCavoForCompatibility'.", "ObjectExpression", "unexpected", "Duplicate key 'getRevisioneCorrente'.", "Duplicate key 'getCaviInstallati'.", "'Stack' is defined but never used.", "'PieChart' is defined but never used.", "'Pie' is defined but never used.", "'Cell' is defined but never used.", "'BarChart' is defined but never used.", "'Bar' is defined but never used.", "'Legend' is defined but never used.", "'progressData' is assigned a value but never used.", "'caviData' is assigned a value but never used.", "'metricsData' is assigned a value but never used.", "'CustomTooltip' is assigned a value but never used.", "'renderCustomizedLabel' is assigned a value but never used.", "'LineChart' is defined but never used.", "'XAxis' is defined but never used.", "'YAxis' is defined but never used.", "'CartesianGrid' is defined but never used.", "'ResponsiveContainer' is defined but never used.", "'ComposedChart' is defined but never used.", "'Line' is defined but never used.", "'Chip' is defined but never used.", "'bobineData' is assigned a value but never used.", "'totaliData' is assigned a value but never used.", "'analisiData' is assigned a value but never used.", "'isCompleto' is assigned a value but never used.", "'isInCorso' is assigned a value but never used.", "'ScatterChart' is defined but never used.", "'Scatter' is defined but never used.", "'efficienzaData' is assigned a value but never used.", "'ScatterTooltip' is assigned a value but never used.", "React Hook useEffect has a missing dependency: 'loadCavi'. Either include it or remove the dependency array.", ["982"], "'DownloadIcon' is defined but never used.", "'CABLE_STATES' is defined but never used.", "'REEL_STATES' is defined but never used.", "'determineCableState' is defined but never used.", "'determineReelState' is defined but never used.", "'canModifyCable' is defined but never used.", "'getReelStateColor' is defined but never used.", "'redirectToVisualizzaCavi' is defined but never used.", "'loading' is assigned a value but never used.", "'setLoading' is assigned a value but never used.", "React Hook useEffect has missing dependencies: 'loadBobine' and 'loadCavi'. Either include them or remove the dependency array.", ["983"], "React Hook useEffect has a missing dependency: 'filterCompatibleBobine'. Either include it or remove the dependency array.", ["984"], "'TextField' is defined but never used.", "'List' is defined but never used.", "'ListItem' is defined but never used.", "'ListItemText' is defined but never used.", ["985"], "'FormControl' is defined but never used.", "'ListItemSecondaryAction' is defined but never used.", "'isCableSpare' is defined but never used.", "'isCableInstalled' is defined but never used.", "'getCableStateColor' is defined but never used.", "'searchResults' is assigned a value but never used.", "'setSearchResults' is assigned a value but never used.", "'showSearchResults' is assigned a value but never used.", "'compatibleBobine' is assigned a value but never used.", ["986"], "React Hook useEffect has a missing dependency: 'loadBobine'. Either include it or remove the dependency array.", ["987"], "React Hook useEffect has a missing dependency: 'onError'. Either include it or remove the dependency array. If 'onError' changes too often, find the parent component that defines it and wrap that definition in useCallback.", ["988"], "'bobina' is assigned a value but never used.", "'SaveIcon' is defined but never used.", "'incompatibleReel' is assigned a value but never used.", "'setIncompatibleReel' is assigned a value but never used.", ["989"], "'handleBack' is assigned a value but never used.", "'buildFullBobinaId' is assigned a value but never used.", "'hasSufficientMeters' is assigned a value but never used.", "'getStepContent' is assigned a value but never used.", "'sentData' is assigned a value but never used.", "'FormControlLabel' is defined but never used.", ["990"], "'result' is assigned a value but never used.", "'Alert' is defined but never used.", "'Box' is defined but never used.", {"desc": "991", "fix": "992"}, {"desc": "993", "fix": "994"}, {"desc": "995", "fix": "996"}, {"desc": "997", "fix": "998"}, {"desc": "999", "fix": "1000"}, {"desc": "1001", "fix": "1002"}, {"kind": "1003", "justification": "1004"}, {"desc": "1005", "fix": "1006"}, {"desc": "1007", "fix": "1008"}, {"desc": "1009", "fix": "1010"}, {"desc": "1011", "fix": "1012"}, {"desc": "1011", "fix": "1013"}, {"desc": "1014", "fix": "1015"}, {"desc": "1016", "fix": "1017"}, {"desc": "1018", "fix": "1019"}, {"desc": "1020", "fix": "1021"}, "Update the dependencies array to be: [error, filters, user]", {"range": "1022", "text": "1023"}, "Update the dependencies array to be: [cantiereId, selectCantiere]", {"range": "1024", "text": "1025"}, "Update the dependencies array to be: [cantiereId, loadCertificazioni]", {"range": "1026", "text": "1027"}, "Update the dependencies array to be: [cantiereId, loadComande]", {"range": "1028", "text": "1029"}, "Update the dependencies array to be: [handleOptionSelect, initialOption, loadBobine]", {"range": "1030", "text": "1031"}, "Update the dependencies array to be: [initialOption, loadCavi]", {"range": "1032", "text": "1033"}, "directive", "", "Update the dependencies array to be: [certificazione, cantiereId, loadCavi]", {"range": "1034", "text": "1035"}, "Update the dependencies array to be: [cantiereId, loadBobine, loadCavi]", {"range": "1036", "text": "1037"}, "Update the dependencies array to be: [selectedCavo, bobine, filterCompatibleBobine]", {"range": "1038", "text": "1039"}, "Update the dependencies array to be: [cantiereId, loadCavi]", {"range": "1040", "text": "1041"}, {"range": "1042", "text": "1041"}, "Update the dependencies array to be: [loadBobine, selectedCavo]", {"range": "1043", "text": "1044"}, "Update the dependencies array to be: [cavoId, cavi, selectedCavo, onError]", {"range": "1045", "text": "1046"}, "Update the dependencies array to be: [cantiereId, loadBobine]", {"range": "1047", "text": "1048"}, "Update the dependencies array to be: [open, bobina, cantiereId, loadCavi]", {"range": "1049", "text": "1050"}, [19729, 19738], "[error, filters, user]", [1559, 1571], "[cantiereId, selectCantiere]", [3538, 3550], "[cantiereId, loadCertificazioni]", [2335, 2347], "[cantiereId, loadComande]", [4642, 4644], "[handleOptionSelect, initialOption, loadBobine]", [3043, 3058], "[initialOption, loadCavi]", [1578, 1606], "[certificazione, cantiereId, loadCavi]", [2572, 2584], "[cantiereId, loadBobine, loadCavi]", [14450, 14472], "[selectedCavo, bobine, filterCompatibleBobine]", [1014, 1026], "[cantiereId, loadCavi]", [3142, 3154], [3288, 3302], "[load<PERSON><PERSON><PERSON>, selected<PERSON>avo]", [3868, 3896], "[cavoId, cavi, selected<PERSON>av<PERSON>, onError]", [4325, 4337], "[cantiereId, loadBobine]", [1912, 1938], "[open, bobina, cantiereId, loadCavi]"]